import React, { useState } from 'react';
import { Layout as AntLayout, Menu, Avatar, Dropdown, Button, Space } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ProjectOutlined,
  UserOutlined,
  TeamOutlined,
  BookOutlined,
  FileTextOutlined,
  GlobalOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  ShareAltOutlined,
  RobotOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  LogoutOutlined,
  BellOutlined,
  EnvironmentOutlined,
  EyeOutlined,
  ShoppingOutlined,
  HeartOutlined,
  CompassOutlined,
  StarOutlined,
  CrownOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = AntLayout;

const Layout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 获取当前项目ID（如果在项目页面中）
  const getProjectId = () => {
    const pathParts = location.pathname.split('/');
    if (pathParts[1] === 'projects' && pathParts[2]) {
      return pathParts[2];
    }
    return null;
  };

  const projectId = getProjectId();

  // 菜单项配置
  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
    // 只有在项目页面中才显示内容管理菜单
    ...(projectId ? [
      {
        key: 'content',
        label: '内容管理',
        type: 'group',
      },
      {
        key: `/projects/${projectId}/characters`,
        icon: <UserOutlined />,
        label: '人物管理',
      },
      {
        key: `/projects/${projectId}/factions`,
        icon: <TeamOutlined />,
        label: '势力管理',
      },
      {
        key: `/projects/${projectId}/plots`,
        icon: <BookOutlined />,
        label: '剧情管理',
      },
      {
        key: `/projects/${projectId}/chapters`,
        icon: <FileTextOutlined />,
        label: '章节管理',
      },
      {
        key: `/projects/${projectId}/resource-distribution`,
        icon: <EnvironmentOutlined />,
        label: '资源分布',
      },
      {
        key: `/projects/${projectId}/race-distribution`,
        icon: <TeamOutlined />,
        label: '种族分布',
      },
      {
        key: `/projects/${projectId}/secret-realms`,
        icon: <EyeOutlined />,
        label: '秘境分布',
      },
      {
        key: 'settings',
        label: '设定管理',
        type: 'group',
      },
      {
        key: `/projects/${projectId}/world-settings`,
        icon: <GlobalOutlined />,
        label: '世界设定',
      },
      {
        key: `/projects/${projectId}/cultivation-systems`,
        icon: <ThunderboltOutlined />,
        label: '修炼体系',
      },
      {
        key: `/projects/${projectId}/equipment-systems`,
        icon: <ShoppingOutlined />,
        label: '装备体系',
      },
      {
        key: `/projects/${projectId}/pet-systems`,
        icon: <HeartOutlined />,
        label: '宠物体系',
      },
      {
        key: `/projects/${projectId}/map-structures`,
        icon: <CompassOutlined />,
        label: '地图结构',
      },
      {
        key: `/projects/${projectId}/dimension-structures`,
        icon: <StarOutlined />,
        label: '维度结构',
      },
      {
        key: `/projects/${projectId}/spiritual-treasures`,
        icon: <CrownOutlined />,
        label: '灵宝体系',
      },
      {
        key: `/projects/${projectId}/timeline`,
        icon: <ClockCircleOutlined />,
        label: '时间线',
      },
      {
        key: `/projects/${projectId}/relations`,
        icon: <ShareAltOutlined />,
        label: '关系网络',
      },
    ] : []),
    {
      key: 'tools',
      label: '工具',
      type: 'group',
    },
    {
      key: '/ai-assistant',
      icon: <RobotOutlined />,
      label: 'AI助手',
    },
    {
      key: '/ai-test',
      icon: <RobotOutlined />,
      label: 'AI测试',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // 用户菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '偏好设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ];

  const handleMenuClick = ({ key }) => {
    navigate(key);
  };

  const handleUserMenuClick = ({ key }) => {
    switch (key) {
      case 'profile':
        navigate('/profile');
        break;
      case 'settings':
        navigate('/settings');
        break;
      case 'logout':
        // 处理退出登录
        console.log('退出登录');
        break;
      default:
        break;
    }
  };

  return (
    <AntLayout className="layout-container">
      <Header className="layout-header">
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ marginRight: 16 }}
            />
            <h1 style={{ margin: 0, color: '#1890ff', fontSize: '20px', fontWeight: 'bold' }}>
              NovelCraft
            </h1>
          </div>

          <Space>
            <Button type="text" icon={<BellOutlined />} />
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick,
              }}
              placement="bottomRight"
            >
              <Space style={{ cursor: 'pointer' }}>
                <Avatar icon={<UserOutlined />} />
                <span>用户</span>
              </Space>
            </Dropdown>
          </Space>
        </div>
      </Header>

      <AntLayout className="layout-content">
        <Sider
          className="layout-sider"
          collapsed={collapsed}
          width={240}
          collapsedWidth={80}
          theme="light"
        >
          <Menu
            mode="inline"
            selectedKeys={[location.pathname]}
            items={menuItems}
            onClick={handleMenuClick}
            style={{ height: '100%', borderRight: 0 }}
          />
        </Sider>

        <Content className="layout-main">
          {children}
        </Content>
      </AntLayout>
    </AntLayout>
  );
};

export default Layout;
