[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterList.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "19", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js": "20", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js": "21", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js": "22", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js": "23", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js": "24", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js": "25", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js": "26", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js": "27"}, {"size": 254, "mtime": 1748329246695, "results": "28", "hashOfConfig": "29"}, {"size": 4160, "mtime": 1748487526921, "results": "30", "hashOfConfig": "29"}, {"size": 6760, "mtime": 1748487548250, "results": "31", "hashOfConfig": "29"}, {"size": 9147, "mtime": 1748329002570, "results": "32", "hashOfConfig": "29"}, {"size": 18761, "mtime": 1748351266251, "results": "33", "hashOfConfig": "29"}, {"size": 10615, "mtime": 1748329052986, "results": "34", "hashOfConfig": "29"}, {"size": 9758, "mtime": 1748329098388, "results": "35", "hashOfConfig": "29"}, {"size": 20451, "mtime": 1748353392933, "results": "36", "hashOfConfig": "29"}, {"size": 9954, "mtime": 1748351175294, "results": "37", "hashOfConfig": "29"}, {"size": 29827, "mtime": 1748483630000, "results": "38", "hashOfConfig": "29"}, {"size": 24215, "mtime": 1748352877767, "results": "39", "hashOfConfig": "29"}, {"size": 23256, "mtime": 1748352524186, "results": "40", "hashOfConfig": "29"}, {"size": 22880, "mtime": 1748353123046, "results": "41", "hashOfConfig": "29"}, {"size": 44598, "mtime": 1748416357901, "results": "42", "hashOfConfig": "29"}, {"size": 19283, "mtime": 1748352994687, "results": "43", "hashOfConfig": "29"}, {"size": 4503, "mtime": 1748341004213, "results": "44", "hashOfConfig": "29"}, {"size": 26413, "mtime": 1748397029776, "results": "45", "hashOfConfig": "29"}, {"size": 5891, "mtime": 1748357289630, "results": "46", "hashOfConfig": "29"}, {"size": 8296, "mtime": 1748361420913, "results": "47", "hashOfConfig": "29"}, {"size": 16897, "mtime": 1748487115368, "results": "48", "hashOfConfig": "29"}, {"size": 18146, "mtime": 1748487204858, "results": "49", "hashOfConfig": "29"}, {"size": 15353, "mtime": 1748486959232, "results": "50", "hashOfConfig": "29"}, {"size": 15084, "mtime": 1748486881151, "results": "51", "hashOfConfig": "29"}, {"size": 14317, "mtime": 1748487034064, "results": "52", "hashOfConfig": "29"}, {"size": 17842, "mtime": 1748487380513, "results": "53", "hashOfConfig": "29"}, {"size": 21003, "mtime": 1748487480069, "results": "54", "hashOfConfig": "29"}, {"size": 19068, "mtime": 1748487295697, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["137", "138", "139", "140"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["141", "142", "143", "144"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", ["145"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["146"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["147"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterList.js", ["148", "149"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["150", "151"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["152"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["153", "154"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["155", "156", "157"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["158", "159"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["160"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["161", "162"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["163"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\EquipmentSystems.js", ["164", "165", "166"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PetSystems.js", ["167", "168"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\RaceDistribution.js", ["169"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ResourceDistribution.js", ["170", "171"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SecretRealms.js", ["172", "173"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\DimensionStructure.js", ["174"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\SpiritualTreasureSystems.js", ["175"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\MapStructure.js", ["176", "177"], [], {"ruleId": "178", "severity": 1, "message": "179", "line": 1, "column": 27, "nodeType": "180", "messageId": "181", "endLine": 1, "endColumn": 36}, {"ruleId": "178", "severity": 1, "message": "182", "line": 18, "column": 17, "nodeType": "180", "messageId": "181", "endLine": 18, "endColumn": 25}, {"ruleId": "178", "severity": 1, "message": "183", "line": 25, "column": 26, "nodeType": "180", "messageId": "181", "endLine": 25, "endColumn": 43}, {"ruleId": "178", "severity": 1, "message": "184", "line": 55, "column": 28, "nodeType": "180", "messageId": "181", "endLine": 55, "endColumn": 47}, {"ruleId": "178", "severity": 1, "message": "185", "line": 21, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 21, "endColumn": 9}, {"ruleId": "178", "severity": 1, "message": "186", "line": 34, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 34, "endColumn": 17}, {"ruleId": "178", "severity": 1, "message": "187", "line": 37, "column": 22, "nodeType": "180", "messageId": "181", "endLine": 37, "endColumn": 31}, {"ruleId": "188", "severity": 1, "message": "189", "line": 107, "column": 6, "nodeType": "190", "endLine": 107, "endColumn": 8, "suggestions": "191"}, {"ruleId": "188", "severity": 1, "message": "192", "line": 90, "column": 6, "nodeType": "190", "endLine": 90, "endColumn": 8, "suggestions": "193"}, {"ruleId": "188", "severity": 1, "message": "194", "line": 55, "column": 6, "nodeType": "190", "endLine": 55, "endColumn": 10, "suggestions": "195"}, {"ruleId": "188", "severity": 1, "message": "196", "line": 112, "column": 6, "nodeType": "190", "endLine": 112, "endColumn": 8, "suggestions": "197"}, {"ruleId": "178", "severity": 1, "message": "198", "line": 15, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 15, "endColumn": 11}, {"ruleId": "188", "severity": 1, "message": "199", "line": 80, "column": 6, "nodeType": "190", "endLine": 80, "endColumn": 8, "suggestions": "200"}, {"ruleId": "178", "severity": 1, "message": "201", "line": 19, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 19, "endColumn": 15}, {"ruleId": "188", "severity": 1, "message": "202", "line": 116, "column": 6, "nodeType": "190", "endLine": 116, "endColumn": 8, "suggestions": "203"}, {"ruleId": "188", "severity": 1, "message": "204", "line": 131, "column": 6, "nodeType": "190", "endLine": 131, "endColumn": 8, "suggestions": "205"}, {"ruleId": "188", "severity": 1, "message": "206", "line": 109, "column": 6, "nodeType": "190", "endLine": 109, "endColumn": 8, "suggestions": "207"}, {"ruleId": "178", "severity": 1, "message": "208", "line": 344, "column": 9, "nodeType": "180", "messageId": "181", "endLine": 344, "endColumn": 23}, {"ruleId": "178", "severity": 1, "message": "209", "line": 22, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 22, "endColumn": 10}, {"ruleId": "178", "severity": 1, "message": "210", "line": 32, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 32, "endColumn": 18}, {"ruleId": "188", "severity": 1, "message": "211", "line": 148, "column": 6, "nodeType": "190", "endLine": 148, "endColumn": 8, "suggestions": "212"}, {"ruleId": "178", "severity": 1, "message": "209", "line": 20, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 20, "endColumn": 10}, {"ruleId": "188", "severity": 1, "message": "213", "line": 115, "column": 6, "nodeType": "190", "endLine": 115, "endColumn": 8, "suggestions": "214"}, {"ruleId": "178", "severity": 1, "message": "215", "line": 23, "column": 16, "nodeType": "180", "messageId": "181", "endLine": 23, "endColumn": 20}, {"ruleId": "178", "severity": 1, "message": "216", "line": 108, "column": 10, "nodeType": "180", "messageId": "181", "endLine": 108, "endColumn": 19}, {"ruleId": "188", "severity": 1, "message": "217", "line": 371, "column": 6, "nodeType": "190", "endLine": 371, "endColumn": 8, "suggestions": "218"}, {"ruleId": "188", "severity": 1, "message": "219", "line": 133, "column": 6, "nodeType": "190", "endLine": 133, "endColumn": 8, "suggestions": "220"}, {"ruleId": "178", "severity": 1, "message": "198", "line": 22, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 22, "endColumn": 11}, {"ruleId": "178", "severity": 1, "message": "221", "line": 33, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 33, "endColumn": 15}, {"ruleId": "188", "severity": 1, "message": "222", "line": 102, "column": 6, "nodeType": "190", "endLine": 102, "endColumn": 17, "suggestions": "223"}, {"ruleId": "178", "severity": 1, "message": "224", "line": 31, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 31, "endColumn": 17}, {"ruleId": "188", "severity": 1, "message": "225", "line": 112, "column": 6, "nodeType": "190", "endLine": 112, "endColumn": 17, "suggestions": "226"}, {"ruleId": "188", "severity": 1, "message": "227", "line": 87, "column": 6, "nodeType": "190", "endLine": 87, "endColumn": 17, "suggestions": "228"}, {"ruleId": "178", "severity": 1, "message": "198", "line": 21, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 21, "endColumn": 11}, {"ruleId": "188", "severity": 1, "message": "229", "line": 81, "column": 6, "nodeType": "190", "endLine": 81, "endColumn": 17, "suggestions": "230"}, {"ruleId": "178", "severity": 1, "message": "198", "line": 22, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 22, "endColumn": 11}, {"ruleId": "188", "severity": 1, "message": "231", "line": 87, "column": 6, "nodeType": "190", "endLine": 87, "endColumn": 17, "suggestions": "232"}, {"ruleId": "188", "severity": 1, "message": "233", "line": 112, "column": 6, "nodeType": "190", "endLine": 112, "endColumn": 17, "suggestions": "234"}, {"ruleId": "188", "severity": 1, "message": "235", "line": 140, "column": 6, "nodeType": "190", "endLine": 140, "endColumn": 17, "suggestions": "236"}, {"ruleId": "178", "severity": 1, "message": "237", "line": 31, "column": 3, "nodeType": "180", "messageId": "181", "endLine": 31, "endColumn": 15}, {"ruleId": "188", "severity": 1, "message": "238", "line": 106, "column": 6, "nodeType": "190", "endLine": 106, "endColumn": 17, "suggestions": "239"}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setStats' is assigned a value but never used.", "'setRecentProjects' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'Upload' is defined but never used.", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", "ArrayExpression", ["240"], "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["241"], "React Hook useEffect has a missing dependency: 'loadProject'. Either include it or remove the dependency array.", ["242"], "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["243"], "'Progress' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockChapters'. Either include it or remove the dependency array.", ["244"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["245"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["246"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["247"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["248"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["249"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["250"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["251"], "'StarOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadEquipment'. Either include it or remove the dependency array.", ["252"], "'ShieldOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadPets'. Either include it or remove the dependency array.", ["253"], "React Hook useEffect has a missing dependency: 'loadRaces'. Either include it or remove the dependency array.", ["254"], "React Hook useEffect has a missing dependency: 'loadResources'. Either include it or remove the dependency array.", ["255"], "React Hook useEffect has a missing dependency: 'loadRealms'. Either include it or remove the dependency array.", ["256"], "React Hook useEffect has a missing dependency: 'loadDimensions'. Either include it or remove the dependency array.", ["257"], "React Hook useEffect has a missing dependency: 'loadTreasures'. Either include it or remove the dependency array.", ["258"], "'ShopOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadLocations'. Either include it or remove the dependency array.", ["259"], {"desc": "260", "fix": "261"}, {"desc": "262", "fix": "263"}, {"desc": "264", "fix": "265"}, {"desc": "266", "fix": "267"}, {"desc": "268", "fix": "269"}, {"desc": "270", "fix": "271"}, {"desc": "272", "fix": "273"}, {"desc": "274", "fix": "275"}, {"desc": "276", "fix": "277"}, {"desc": "278", "fix": "279"}, {"desc": "280", "fix": "281"}, {"desc": "282", "fix": "283"}, {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, {"desc": "290", "fix": "291"}, {"desc": "292", "fix": "293"}, {"desc": "294", "fix": "295"}, {"desc": "296", "fix": "297"}, {"desc": "298", "fix": "299"}, "Update the dependencies array to be: [mockCharacters]", {"range": "300", "text": "301"}, "Update the dependencies array to be: [loadProjects]", {"range": "302", "text": "303"}, "Update the dependencies array to be: [id, loadProject]", {"range": "304", "text": "305"}, "Update the dependencies array to be: [mockFactions]", {"range": "306", "text": "307"}, "Update the dependencies array to be: [mockChapters]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [mockSettings]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [mockSystems]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [mockPlots]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [mockEvents]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [mockRelations]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "320", "text": "321"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [loadEquipment, projectId]", {"range": "324", "text": "325"}, "Update the dependencies array to be: [loadPets, projectId]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [loadRaces, projectId]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [loadResources, projectId]", {"range": "330", "text": "331"}, "Update the dependencies array to be: [loadRealms, projectId]", {"range": "332", "text": "333"}, "Update the dependencies array to be: [loadDimensions, projectId]", {"range": "334", "text": "335"}, "Update the dependencies array to be: [loadTreasures, projectId]", {"range": "336", "text": "337"}, "Update the dependencies array to be: [loadLocations, projectId]", {"range": "338", "text": "339"}, [2366, 2368], "[mockCharacters]", [1783, 1785], "[loadProjects]", [1073, 1077], "[id, loadProject]", [2433, 2435], "[mockFactions]", [1603, 1605], "[mockChapters]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2508, 2510], "[mockPlots]", [3453, 3455], "[mockEvents]", [2672, 2674], "[mockRelations]", [9595, 9597], "[fetchAIInfo]", [3499, 3501], "[testAllAPIs]", [2092, 2103], "[loadEquipment, projectId]", [2267, 2278], "[loadPets, projectId]", [1775, 1786], "[loadRaces, projectId]", [1716, 1727], "[loadResources, projectId]", [1850, 1861], "[loadRealms, projectId]", [2509, 2520], "[loadDimensions, projectId]", [3146, 3157], "[loadTreasures, projectId]", [2363, 2374], "[loadLocations, projectId]"]