[{"D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js": "1", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js": "2", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js": "3", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js": "4", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js": "5", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js": "6", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js": "7", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js": "8", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterList.js": "9", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js": "10", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js": "11", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js": "12", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js": "13", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js": "14", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js": "15", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js": "16", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js": "17", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js": "18", "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js": "19"}, {"size": 254, "mtime": 1748329246695, "results": "20", "hashOfConfig": "21"}, {"size": 2935, "mtime": 1748361459110, "results": "22", "hashOfConfig": "21"}, {"size": 6680, "mtime": 1748486023550, "results": "23", "hashOfConfig": "21"}, {"size": 9147, "mtime": 1748329002570, "results": "24", "hashOfConfig": "21"}, {"size": 18761, "mtime": 1748351266251, "results": "25", "hashOfConfig": "21"}, {"size": 10615, "mtime": 1748329052986, "results": "26", "hashOfConfig": "21"}, {"size": 9758, "mtime": 1748329098388, "results": "27", "hashOfConfig": "21"}, {"size": 20451, "mtime": 1748353392933, "results": "28", "hashOfConfig": "21"}, {"size": 9954, "mtime": 1748351175294, "results": "29", "hashOfConfig": "21"}, {"size": 29827, "mtime": 1748483630000, "results": "30", "hashOfConfig": "21"}, {"size": 24215, "mtime": 1748352877767, "results": "31", "hashOfConfig": "21"}, {"size": 23256, "mtime": 1748352524186, "results": "32", "hashOfConfig": "21"}, {"size": 22880, "mtime": 1748353123046, "results": "33", "hashOfConfig": "21"}, {"size": 44598, "mtime": 1748416357901, "results": "34", "hashOfConfig": "21"}, {"size": 19283, "mtime": 1748352994687, "results": "35", "hashOfConfig": "21"}, {"size": 4503, "mtime": 1748341004213, "results": "36", "hashOfConfig": "21"}, {"size": 26413, "mtime": 1748397029776, "results": "37", "hashOfConfig": "21"}, {"size": 5891, "mtime": 1748357289630, "results": "38", "hashOfConfig": "21"}, {"size": 8296, "mtime": 1748361420913, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "<PERSON><PERSON><PERSON>", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\index.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\App.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\Layout.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Dashboard.js", ["97", "98", "99", "100"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CharacterList.js", ["101", "102", "103", "104"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectList.js", ["105"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ProjectDetail.js", ["106"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\FactionList.js", ["107"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\ChapterList.js", ["108", "109"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\WorldSettings.js", ["110", "111"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\CultivationSystems.js", ["112"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\PlotList.js", ["113", "114"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Timeline.js", ["115", "116", "117"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AIAssistant.js", [], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Relations.js", ["118", "119"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\Settings.js", ["120"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\components\\AIConfigPanel.js", ["121", "122"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\AITest.js", ["123"], [], "D:\\AI_project\\小说架构管理及AI生成软件\\frontend\\src\\pages\\OllamaTest.js", [], [], {"ruleId": "124", "severity": 1, "message": "125", "line": 1, "column": 27, "nodeType": "126", "messageId": "127", "endLine": 1, "endColumn": 36}, {"ruleId": "124", "severity": 1, "message": "128", "line": 18, "column": 17, "nodeType": "126", "messageId": "127", "endLine": 18, "endColumn": 25}, {"ruleId": "124", "severity": 1, "message": "129", "line": 25, "column": 26, "nodeType": "126", "messageId": "127", "endLine": 25, "endColumn": 43}, {"ruleId": "124", "severity": 1, "message": "130", "line": 55, "column": 28, "nodeType": "126", "messageId": "127", "endLine": 55, "endColumn": 47}, {"ruleId": "124", "severity": 1, "message": "131", "line": 21, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 21, "endColumn": 9}, {"ruleId": "124", "severity": 1, "message": "132", "line": 34, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 34, "endColumn": 17}, {"ruleId": "124", "severity": 1, "message": "133", "line": 37, "column": 22, "nodeType": "126", "messageId": "127", "endLine": 37, "endColumn": 31}, {"ruleId": "134", "severity": 1, "message": "135", "line": 107, "column": 6, "nodeType": "136", "endLine": 107, "endColumn": 8, "suggestions": "137"}, {"ruleId": "134", "severity": 1, "message": "138", "line": 90, "column": 6, "nodeType": "136", "endLine": 90, "endColumn": 8, "suggestions": "139"}, {"ruleId": "134", "severity": 1, "message": "140", "line": 55, "column": 6, "nodeType": "136", "endLine": 55, "endColumn": 10, "suggestions": "141"}, {"ruleId": "134", "severity": 1, "message": "142", "line": 112, "column": 6, "nodeType": "136", "endLine": 112, "endColumn": 8, "suggestions": "143"}, {"ruleId": "124", "severity": 1, "message": "144", "line": 15, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 15, "endColumn": 11}, {"ruleId": "134", "severity": 1, "message": "145", "line": 80, "column": 6, "nodeType": "136", "endLine": 80, "endColumn": 8, "suggestions": "146"}, {"ruleId": "124", "severity": 1, "message": "147", "line": 19, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 19, "endColumn": 15}, {"ruleId": "134", "severity": 1, "message": "148", "line": 116, "column": 6, "nodeType": "136", "endLine": 116, "endColumn": 8, "suggestions": "149"}, {"ruleId": "134", "severity": 1, "message": "150", "line": 131, "column": 6, "nodeType": "136", "endLine": 131, "endColumn": 8, "suggestions": "151"}, {"ruleId": "134", "severity": 1, "message": "152", "line": 109, "column": 6, "nodeType": "136", "endLine": 109, "endColumn": 8, "suggestions": "153"}, {"ruleId": "124", "severity": 1, "message": "154", "line": 344, "column": 9, "nodeType": "126", "messageId": "127", "endLine": 344, "endColumn": 23}, {"ruleId": "124", "severity": 1, "message": "155", "line": 22, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 22, "endColumn": 10}, {"ruleId": "124", "severity": 1, "message": "156", "line": 32, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 32, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "157", "line": 148, "column": 6, "nodeType": "136", "endLine": 148, "endColumn": 8, "suggestions": "158"}, {"ruleId": "124", "severity": 1, "message": "155", "line": 20, "column": 3, "nodeType": "126", "messageId": "127", "endLine": 20, "endColumn": 10}, {"ruleId": "134", "severity": 1, "message": "159", "line": 115, "column": 6, "nodeType": "136", "endLine": 115, "endColumn": 8, "suggestions": "160"}, {"ruleId": "124", "severity": 1, "message": "161", "line": 23, "column": 16, "nodeType": "126", "messageId": "127", "endLine": 23, "endColumn": 20}, {"ruleId": "124", "severity": 1, "message": "162", "line": 108, "column": 10, "nodeType": "126", "messageId": "127", "endLine": 108, "endColumn": 19}, {"ruleId": "134", "severity": 1, "message": "163", "line": 371, "column": 6, "nodeType": "136", "endLine": 371, "endColumn": 8, "suggestions": "164"}, {"ruleId": "134", "severity": 1, "message": "165", "line": 133, "column": 6, "nodeType": "136", "endLine": 133, "endColumn": 8, "suggestions": "166"}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'setStats' is assigned a value but never used.", "'setRecentProjects' is assigned a value but never used.", "'setRecentActivities' is assigned a value but never used.", "'Upload' is defined but never used.", "'UploadOutlined' is defined but never used.", "'Paragraph' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'mockCharacters'. Either include it or remove the dependency array.", "ArrayExpression", ["167"], "React Hook useEffect has a missing dependency: 'loadProjects'. Either include it or remove the dependency array.", ["168"], "React Hook useEffect has a missing dependency: 'loadProject'. Either include it or remove the dependency array.", ["169"], "React Hook useEffect has a missing dependency: 'mockFactions'. Either include it or remove the dependency array.", ["170"], "'Progress' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockChapters'. Either include it or remove the dependency array.", ["171"], "'Descriptions' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockSettings'. Either include it or remove the dependency array.", ["172"], "React Hook useEffect has a missing dependency: 'mockSystems'. Either include it or remove the dependency array.", ["173"], "React Hook useEffect has a missing dependency: 'mockPlots'. Either include it or remove the dependency array.", ["174"], "'completedPlots' is assigned a value but never used.", "'Divider' is defined but never used.", "'HistoryOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'mockEvents'. Either include it or remove the dependency array.", ["175"], "React Hook useEffect has a missing dependency: 'mockRelations'. Either include it or remove the dependency array.", ["176"], "'Text' is assigned a value but never used.", "'providers' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchAIInfo'. Either include it or remove the dependency array.", ["177"], "React Hook useEffect has a missing dependency: 'testAllAPIs'. Either include it or remove the dependency array.", ["178"], {"desc": "179", "fix": "180"}, {"desc": "181", "fix": "182"}, {"desc": "183", "fix": "184"}, {"desc": "185", "fix": "186"}, {"desc": "187", "fix": "188"}, {"desc": "189", "fix": "190"}, {"desc": "191", "fix": "192"}, {"desc": "193", "fix": "194"}, {"desc": "195", "fix": "196"}, {"desc": "197", "fix": "198"}, {"desc": "199", "fix": "200"}, {"desc": "201", "fix": "202"}, "Update the dependencies array to be: [mockCharacters]", {"range": "203", "text": "204"}, "Update the dependencies array to be: [loadProjects]", {"range": "205", "text": "206"}, "Update the dependencies array to be: [id, loadProject]", {"range": "207", "text": "208"}, "Update the dependencies array to be: [mockFactions]", {"range": "209", "text": "210"}, "Update the dependencies array to be: [mockChapters]", {"range": "211", "text": "212"}, "Update the dependencies array to be: [mockSettings]", {"range": "213", "text": "214"}, "Update the dependencies array to be: [mockSystems]", {"range": "215", "text": "216"}, "Update the dependencies array to be: [mockPlots]", {"range": "217", "text": "218"}, "Update the dependencies array to be: [mockEvents]", {"range": "219", "text": "220"}, "Update the dependencies array to be: [mockRelations]", {"range": "221", "text": "222"}, "Update the dependencies array to be: [fetchAIInfo]", {"range": "223", "text": "224"}, "Update the dependencies array to be: [testAllAPIs]", {"range": "225", "text": "226"}, [2366, 2368], "[mockCharacters]", [1783, 1785], "[loadProjects]", [1073, 1077], "[id, loadProject]", [2433, 2435], "[mockFactions]", [1603, 1605], "[mockChapters]", [2658, 2660], "[mockSettings]", [4240, 4242], "[mockSystems]", [2508, 2510], "[mockPlots]", [3453, 3455], "[mockEvents]", [2672, 2674], "[mockRelations]", [9595, 9597], "[fetchAIInfo]", [3499, 3501], "[testAllAPIs]"]